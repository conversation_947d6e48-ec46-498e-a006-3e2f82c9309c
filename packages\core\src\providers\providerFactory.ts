/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { GoogleGenAI } from '@google/genai';
import { BaseProvider, ProviderConfig } from './baseProvider.js';
import { OpenAIProvider } from './openaiProvider.js';
import { AuthType, ContentGeneratorConfig } from '../core/contentGenerator.js';
import { PROVIDER_CONFIGS, ModelProvider } from '../models/modelProvider.js';
import { Config } from '../config/config.js';
import { createCodeAssistContentGenerator } from '../code_assist/codeAssist.js';

export interface ProviderFactoryOptions {
  config: ContentGeneratorConfig;
  gcConfig: Config;
  sessionId?: string;
}

export class ProviderFactory {
  static async createProvider(options: ProviderFactoryOptions): Promise<BaseProvider> {
    const { config, gcConfig, sessionId } = options;
    const version = process.env.CLI_VERSION || process.version;
    const httpOptions = {
      headers: {
        'User-Agent': `GeminiCLI/${version} (${process.platform}; ${process.arch})`,
      },
    };

    // Google providers (existing logic)
    if (
      config.authType === AuthType.LOGIN_WITH_GOOGLE ||
      config.authType === AuthType.CLOUD_SHELL
    ) {
      return createCodeAssistContentGenerator(
        httpOptions,
        config.authType,
        gcConfig,
        sessionId,
      );
    }

    if (
      config.authType === AuthType.USE_GEMINI ||
      config.authType === AuthType.USE_VERTEX_AI
    ) {
      const googleGenAI = new GoogleGenAI({
        apiKey: config.apiKey === '' ? undefined : config.apiKey,
        vertexai: config.vertexai,
        httpOptions,
      });

      return googleGenAI.models;
    }

    // OpenAI compatible providers
    return this.createOpenAICompatibleProvider(config);
  }

  private static createOpenAICompatibleProvider(config: ContentGeneratorConfig): BaseProvider {
    const providerConfig: ProviderConfig = {
      apiKey: config.apiKey || '',
      model: config.model,
      proxy: config.proxy,
    };

    switch (config.authType) {
      case AuthType.USE_OPENAI:
        return new OpenAIProvider({
          ...providerConfig,
          baseUrl: PROVIDER_CONFIGS[ModelProvider.OPENAI].baseUrl,
          headers: PROVIDER_CONFIGS[ModelProvider.OPENAI].headers,
        });

      case AuthType.USE_ANTHROPIC:
        return new OpenAIProvider({
          ...providerConfig,
          baseUrl: PROVIDER_CONFIGS[ModelProvider.ANTHROPIC].baseUrl,
          headers: PROVIDER_CONFIGS[ModelProvider.ANTHROPIC].headers,
        });

      case AuthType.USE_MISTRAL:
        return new OpenAIProvider({
          ...providerConfig,
          baseUrl: PROVIDER_CONFIGS[ModelProvider.MISTRAL].baseUrl,
          headers: PROVIDER_CONFIGS[ModelProvider.MISTRAL].headers,
        });

      case AuthType.USE_OPENROUTER:
        return new OpenAIProvider({
          ...providerConfig,
          baseUrl: PROVIDER_CONFIGS[ModelProvider.OPENROUTER].baseUrl,
          headers: PROVIDER_CONFIGS[ModelProvider.OPENROUTER].headers,
        });

      case AuthType.USE_CUSTOM:
        return new OpenAIProvider({
          ...providerConfig,
          baseUrl: process.env.CUSTOM_BASE_URL || PROVIDER_CONFIGS[ModelProvider.CUSTOM].baseUrl,
          headers: PROVIDER_CONFIGS[ModelProvider.CUSTOM].headers,
        });

      default:
        throw new Error(
          `Error creating provider: Unsupported authType: ${config.authType}`,
        );
    }
  }

  static getSupportedProviders(): string[] {
    return ['google', 'openai', 'anthropic', 'mistral', 'openrouter', 'custom'];
  }

  static getProviderFromAuthType(authType: AuthType): string {
    switch (authType) {
      case AuthType.LOGIN_WITH_GOOGLE:
      case AuthType.CLOUD_SHELL:
      case AuthType.USE_GEMINI:
      case AuthType.USE_VERTEX_AI:
        return 'google';
      case AuthType.USE_OPENAI:
        return 'openai';
      case AuthType.USE_ANTHROPIC:
        return 'anthropic';
      case AuthType.USE_MISTRAL:
        return 'mistral';
      case AuthType.USE_OPENROUTER:
        return 'openrouter';
      case AuthType.USE_CUSTOM:
        return 'custom';
      default:
        return 'google';
    }
  }
}
