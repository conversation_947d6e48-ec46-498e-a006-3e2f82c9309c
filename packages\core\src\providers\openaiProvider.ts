/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
  Part,
  Content,
  ContentListUnion,
  PartUnion,
} from '@google/genai';
import { BaseProviderImpl, ProviderConfig } from './baseProvider.js';

interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenAIStreamResponse {
  choices: Array<{
    delta: {
      content?: string;
    };
  }>;
}

interface OpenAIEmbeddingRequest {
  model: string;
  input: string | string[];
}

interface OpenAIEmbeddingResponse {
  data: Array<{
    embedding: number[];
  }>;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export class OpenAIProvider extends BaseProviderImpl {
  constructor(config: ProviderConfig) {
    super({
      ...config,
      baseUrl: config.baseUrl || 'https://api.openai.com/v1',
    });
  }

  async generateContent(request: GenerateContentParameters): Promise<GenerateContentResponse> {
    const openAIRequest = this.convertToOpenAIRequest(request);
    
    const response = await fetch(`${this.getBaseUrl()}/chat/completions`, {
      method: 'POST',
      headers: {
        ...this.getHeaders(),
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
      body: JSON.stringify(openAIRequest),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data: OpenAIResponse = await response.json();
    return this.convertFromOpenAIResponse(data);
  }

  async generateContentStream(request: GenerateContentParameters): Promise<AsyncGenerator<GenerateContentResponse>> {
    const openAIRequest = {
      ...this.convertToOpenAIRequest(request),
      stream: true,
    };

    const response = await fetch(`${this.getBaseUrl()}/chat/completions`, {
      method: 'POST',
      headers: {
        ...this.getHeaders(),
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
      body: JSON.stringify(openAIRequest),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    return this.createStreamGenerator(reader);
  }

  async countTokens(request: CountTokensParameters): Promise<CountTokensResponse> {
    // OpenAI doesn't have a direct token counting endpoint
    // We'll use a rough estimation based on the text
    const text = this.extractTextFromContents(request.contents);
    const tokenCount = Math.ceil(text.length / 4); // Rough estimation
    
    return {
      totalTokens: tokenCount,
    };
  }

  async embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse> {
    const text = this.extractTextFromContents(request.contents);
    
    const embeddingRequest: OpenAIEmbeddingRequest = {
      model: 'text-embedding-ada-002',
      input: text,
    };

    const response = await fetch(`${this.getBaseUrl()}/embeddings`, {
      method: 'POST',
      headers: {
        ...this.getHeaders(),
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
      body: JSON.stringify(embeddingRequest),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data: OpenAIEmbeddingResponse = await response.json();
    
    return {
      embeddings: [{
        values: data.data[0].embedding,
      }],
    };
  }

  private async* createStreamGenerator(reader: ReadableStreamDefaultReader<Uint8Array>): AsyncGenerator<GenerateContentResponse> {
    let buffer = '';
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += new TextDecoder().decode(value);
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') return;

          try {
            const parsed: OpenAIStreamResponse = JSON.parse(data);
            if (parsed.choices?.[0]?.delta?.content) {
              yield {
                candidates: [{
                  content: {
                    parts: [{
                      text: parsed.choices[0].delta.content,
                    }],
                  },
                }],
                text: parsed.choices[0].delta.content,
                data: undefined,
                functionCalls: undefined,
                executableCode: undefined,
                codeExecutionResult: undefined,
              };
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }
      }
    }
  }

  private convertToOpenAIRequest(request: GenerateContentParameters): OpenAIRequest {
    const messages: OpenAIMessage[] = [];
    
    if (request.contents) {
      const contents = this.normalizeContents(request.contents);
      for (const content of contents) {
        if (typeof content === 'string') {
          messages.push({
            role: 'user',
            content: content,
          });
        } else if (content && typeof content === 'object') {
          if (content.role === 'user') {
            messages.push({
              role: 'user',
              content: this.extractTextFromParts(content.parts || []),
            });
          } else if (content.role === 'model') {
            messages.push({
              role: 'assistant',
              content: this.extractTextFromParts(content.parts || []),
            });
          }
        }
      }
    }

    return {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
    };
  }

  private convertFromOpenAIResponse(response: OpenAIResponse): GenerateContentResponse {
    const content = response.choices[0]?.message?.content || '';
    return {
      candidates: [{
        content: {
          parts: [{
            text: content,
          }],
        },
      }],
      text: content,
      usageMetadata: response.usage ? {
        promptTokenCount: response.usage.prompt_tokens,
        candidatesTokenCount: response.usage.completion_tokens,
        totalTokenCount: response.usage.total_tokens,
      } : undefined,
      data: undefined,
      functionCalls: undefined,
      executableCode: undefined,
      codeExecutionResult: undefined,
    };
  }

  private extractTextFromParts(parts: Part[] | PartUnion[]): string {
    if (!parts) return '';
    
    return parts
      .filter(part => part && typeof part === 'object' && 'text' in part)
      .map(part => (part as any).text || '')
      .join('');
  }

  private extractTextFromContents(contents: ContentListUnion): string {
    if (!contents) return '';
    
    if (typeof contents === 'string') {
      return contents;
    }
    
    if (Array.isArray(contents)) {
      return contents
        .flatMap(content => this.extractTextFromContent(content))
        .join('');
    }
    
    return this.extractTextFromContent(contents);
  }

  private extractTextFromContent(content: Content | string | Part): string {
    if (typeof content === 'string') {
      return content;
    }
    
    if (!content || typeof content !== 'object') {
      return '';
    }
    
    if ('text' in content) {
      return (content as any).text || '';
    }
    
    if ('parts' in content) {
      return (content.parts || [])
        .filter(part => part && typeof part === 'object' && 'text' in part)
        .map(part => (part as any).text || '')
        .join('');
    }
    
    return '';
  }

  private normalizeContents(contents: ContentListUnion): (Content | string)[] {
    if (!contents) return [];
    
    if (typeof contents === 'string') {
      return [contents];
    }
    
    if (Array.isArray(contents)) {
      return contents.map(item => {
        if (typeof item === 'string') {
          return item;
        }
        if (item && typeof item === 'object' && 'role' in item) {
          return item as Content;
        }
        return '';
      }).filter(item => item !== '');
    }
    
    if (contents && typeof contents === 'object' && 'role' in contents) {
      return [contents as Content];
    }
    
    return [];
  }
}
