/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
} from '@google/genai';
import { UserTierId } from '../code_assist/types.js';

/**
 * Base interface for all AI providers
 */
export interface BaseProvider {
  generateContent(request: GenerateContentParameters): Promise<GenerateContentResponse>;
  generateContentStream(request: GenerateContentParameters): Promise<AsyncGenerator<GenerateContentResponse>>;
  countTokens(request: CountTokensParameters): Promise<CountTokensResponse>;
  embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse>;
  userTier?: UserTierId;
}

/**
 * Provider configuration interface
 */
export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  headers?: Record<string, string>;
  proxy?: string;
}

/**
 * Abstract base class for provider implementations
 */
export abstract class BaseProviderImpl implements BaseProvider {
  protected config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = config;
  }

  abstract generateContent(request: GenerateContentParameters): Promise<GenerateContentResponse>;
  abstract generateContentStream(request: GenerateContentParameters): Promise<AsyncGenerator<GenerateContentResponse>>;
  abstract countTokens(request: CountTokensParameters): Promise<CountTokensResponse>;
  abstract embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse>;

  protected getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      ...this.config.headers,
    };
  }

  protected getBaseUrl(): string {
    return this.config.baseUrl || '';
  }
}
