#!/usr/bin/env node

/**
 * Quick development environment setup
 * Usage: node scripts/setup-dev-env.js
 */

import fs from 'fs';
import path from 'path';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (prompt) => new Promise((resolve) => rl.question(prompt, resolve));

async function setupDevEnv() {
  console.log('🚀 Gemini CLI Development Environment Setup\n');

  const envPath = path.join(process.cwd(), '.env');
  const envExamplePath = path.join(process.cwd(), '.env.example');

  // Check if .env already exists
  if (fs.existsSync(envPath)) {
    const overwrite = await question('📁 .env file already exists. Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      console.log('✅ Keeping existing .env file');
      rl.close();
      return;
    }
  }

  console.log('🔧 Setting up environment variables...\n');

  // Ask for API keys
  const providers = [
    { name: 'OpenAI', env: 'OPENAI_API_KEY', url: 'https://platform.openai.com/api-keys' },
    { name: 'Anthropic', env: 'ANTHROPIC_API_KEY', url: 'https://console.anthropic.com/' },
    { name: 'Mistral', env: 'MISTRAL_API_KEY', url: 'https://console.mistral.ai/' },
    { name: 'OpenRouter', env: 'OPENROUTER_API_KEY', url: 'https://openrouter.ai/keys' },
    { name: 'Google Gemini', env: 'GEMINI_API_KEY', url: 'https://aistudio.google.com/apikey' }
  ];

  const envVars = {};

  for (const provider of providers) {
    console.log(`\n🔑 ${provider.name} API Key`);
    console.log(`   Get it from: ${provider.url}`);
    const key = await question(`   Enter ${provider.name} API key (or press Enter to skip): `);
    
    if (key.trim()) {
      envVars[provider.env] = key.trim();
      console.log(`   ✅ ${provider.name} API key saved`);
    } else {
      envVars[provider.env] = `your_${provider.name.toLowerCase().replace(' ', '_')}_api_key_here`;
      console.log(`   ⏭️  ${provider.name} API key skipped`);
    }
  }

  // Add other common env vars
  envVars['DEBUG'] = 'false';
  envVars['NO_BROWSER'] = 'false';
  envVars['GEMINI_MODEL'] = 'gemini-2.0-flash-exp';

  // Custom provider settings
  envVars['CUSTOM_API_KEY'] = 'your_custom_api_key_here';
  envVars['CUSTOM_BASE_URL'] = 'https://your-custom-endpoint.com/v1';

  // Write .env file
  const envContent = Object.entries(envVars)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');

  fs.writeFileSync(envPath, envContent);

  console.log('\n✅ .env file created successfully!');
  console.log('\n🚀 Quick test commands:');
  console.log('   npm run dev:openai');
  console.log('   npm run dev:anthropic');
  console.log('   npm run dev:mistral');
  console.log('\n💡 Edit .env file to add your real API keys');

  rl.close();
}

setupDevEnv().catch(console.error);
