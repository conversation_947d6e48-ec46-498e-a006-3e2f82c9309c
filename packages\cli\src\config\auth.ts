/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { AuthType } from '@google/gemini-cli-core';
import { loadEnvironment } from './settings.js';

export const getAuthTypeFromProvider = (provider?: string): AuthType | undefined => {
  if (!provider) return undefined;

  switch (provider.toLowerCase()) {
    case 'google':
      return AuthType.USE_GEMINI;
    case 'openai':
      return AuthType.USE_OPENAI;
    case 'anthropic':
      return AuthType.USE_ANTHROPIC;
    case 'mistral':
      return AuthType.USE_MISTRAL;
    case 'openrouter':
      return AuthType.USE_OPENROUTER;
    case 'custom':
      return AuthType.USE_CUSTOM;
    default:
      return undefined;
  }
};

export const detectProviderFromEnvironment = (): string | undefined => {
  // Check environment variable first
  if (process.env.GEMINI_PROVIDER) {
    return process.env.GEMINI_PROVIDER;
  }

  // Auto-detect based on available API keys
  if (process.env.OPENAI_API_KEY) return 'openai';
  if (process.env.ANTHROPIC_API_KEY) return 'anthropic';
  if (process.env.MISTRAL_API_KEY) return 'mistral';
  if (process.env.OPENROUTER_API_KEY) return 'openrouter';
  if (process.env.CUSTOM_API_KEY) return 'custom';
  if (process.env.GEMINI_API_KEY) return 'google';

  return undefined;
};

export const validateAuthMethod = (authMethod: string): string | null => {
  loadEnvironment();
  if (
    authMethod === AuthType.LOGIN_WITH_GOOGLE ||
    authMethod === AuthType.CLOUD_SHELL
  ) {
    return null;
  }

  if (authMethod === AuthType.USE_GEMINI) {
    if (!process.env.GEMINI_API_KEY) {
      return 'GEMINI_API_KEY environment variable not found. Add that to your environment and try again (no reload needed if using .env)!';
    }
    return null;
  }

  if (authMethod === AuthType.USE_VERTEX_AI) {
    const hasVertexProjectLocationConfig =
      !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
      return (
        'When using Vertex AI, you must specify either:\n' +
        '• GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n' +
        '• GOOGLE_API_KEY environment variable (if using express mode).\n' +
        'Update your environment and try again (no reload needed if using .env)!'
      );
    }
    return null;
  }

  if (authMethod === AuthType.USE_OPENAI) {
    if (!process.env.OPENAI_API_KEY) {
      return 'OPENAI_API_KEY environment variable not found. Add that to your environment and try again (no reload needed if using .env)!';
    }
    return null;
  }

  if (authMethod === AuthType.USE_ANTHROPIC) {
    if (!process.env.ANTHROPIC_API_KEY) {
      return 'ANTHROPIC_API_KEY environment variable not found. Add that to your environment and try again (no reload needed if using .env)!';
    }
    return null;
  }

  if (authMethod === AuthType.USE_MISTRAL) {
    if (!process.env.MISTRAL_API_KEY) {
      return 'MISTRAL_API_KEY environment variable not found. Add that to your environment and try again (no reload needed if using .env)!';
    }
    return null;
  }

  if (authMethod === AuthType.USE_OPENROUTER) {
    if (!process.env.OPENROUTER_API_KEY) {
      return 'OPENROUTER_API_KEY environment variable not found. Add that to your environment and try again (no reload needed if using .env)!';
    }
    return null;
  }

  if (authMethod === AuthType.USE_CUSTOM) {
    if (!process.env.CUSTOM_API_KEY) {
      return 'CUSTOM_API_KEY environment variable not found. Add that to your environment and try again (no reload needed if using .env)!';
    }
    return null;
  }

  return 'Invalid auth method selected.';
};
