/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { OpenAIProvider } from './openaiProvider.js';
import { ProviderConfig } from './baseProvider.js';

// Mock fetch globally
global.fetch = vi.fn();

describe('OpenAIProvider', () => {
  let provider: OpenAIProvider;
  let mockConfig: ProviderConfig;

  beforeEach(() => {
    mockConfig = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.openai.com/v1',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1000,
    };
    provider = new OpenAIProvider(mockConfig);
    vi.clearAllMocks();
  });

  describe('generateContent', () => {
    it('should make a successful API call', async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              content: 'Hello, world!',
            },
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
        },
      };

      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const request = {
        model: 'gpt-3.5-turbo',
        contents: 'Test prompt',
      };

      const result = await provider.generateContent(request);

      expect(fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
          },
          body: JSON.stringify({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'user',
                content: 'Test prompt',
              },
            ],
            temperature: 0.7,
            max_tokens: 1000,
          }),
        }
      );

      expect(result).toEqual({
        candidates: [
          {
            content: {
              parts: [
                {
                  text: 'Hello, world!',
                },
              ],
            },
          },
        ],
        text: 'Hello, world!',
        usageMetadata: {
          promptTokenCount: 10,
          candidatesTokenCount: 5,
          totalTokenCount: 15,
        },
        data: undefined,
        functionCalls: undefined,
        executableCode: undefined,
        codeExecutionResult: undefined,
      });
    });

    it('should handle API errors', async () => {
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
      } as Response);

      const request = {
        model: 'gpt-3.5-turbo',
        contents: 'Test prompt',
      };

      await expect(provider.generateContent(request)).rejects.toThrow(
        'OpenAI API error: 401 Unauthorized'
      );
    });
  });

  describe('countTokens', () => {
    it('should estimate token count', async () => {
      const request = {
        model: 'gpt-3.5-turbo',
        contents: 'This is a test prompt with some words',
      };

      const result = await provider.countTokens(request);

      expect(result.totalTokens).toBeGreaterThan(0);
      expect(typeof result.totalTokens).toBe('number');
    });
  });

  describe('embedContent', () => {
    it('should make a successful embedding API call', async () => {
      const mockResponse = {
        data: [
          {
            embedding: [0.1, 0.2, 0.3, 0.4, 0.5],
          },
        ],
        usage: {
          prompt_tokens: 5,
          total_tokens: 5,
        },
      };

      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response);

      const request = {
        model: 'text-embedding-ada-002',
        contents: 'Test text for embedding',
      };

      const result = await provider.embedContent(request);

      expect(fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/embeddings',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
          },
          body: JSON.stringify({
            model: 'text-embedding-ada-002',
            input: 'Test text for embedding',
          }),
        }
      );

      expect(result).toEqual({
        embeddings: [
          {
            values: [0.1, 0.2, 0.3, 0.4, 0.5],
          },
        ],
      });
    });
  });
});
